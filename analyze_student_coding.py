#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生编码分析脚本

分析cls6-merged文件夹下的xlsx文件，统计每个学生在不同讨论类型中的编码情况：
- 人人讨论（日期开头的文件）
- 人AI讨论（id_开头的文件）

输出每个学生每个类别编码的数量和占比
"""

import pandas as pd
import numpy as np
import os
import glob
from collections import defaultdict
import re

def load_student_mapping():
    """加载学生ID到姓名的映射"""
    print("加载学生映射数据...")
    df = pd.read_csv('group_collaboration_data_all_new.csv')
    
    # 创建ID到姓名的映射
    id_to_name = {}
    for _, row in df.iterrows():
        if pd.notna(row['id']) and pd.notna(row['人名']):
            id_to_name[str(row['id'])] = row['人名']
    
    print(f"加载了 {len(id_to_name)} 个学生映射")
    return id_to_name

def get_file_lists():
    """获取文件列表并分类"""
    print("获取文件列表...")
    
    all_files = glob.glob('cls6-merged/*.xlsx')
    print(f"找到 {len(all_files)} 个文件")
    
    # 分类文件
    human_human_files = []  # 人人讨论
    human_ai_files = []     # 人AI讨论
    
    for file in all_files:
        filename = os.path.basename(file)
        if filename.startswith('id_'):
            human_ai_files.append(file)
        elif re.match(r'^\d{4}', filename):  # 以4位数字开头
            human_human_files.append(file)
    
    print(f"人人讨论文件: {len(human_human_files)} 个")
    print(f"人AI讨论文件: {len(human_ai_files)} 个")
    
    return human_human_files, human_ai_files

def extract_student_id_from_filename(filename):
    """从文件名中提取学生ID"""
    if filename.startswith('id_'):
        # 人AI讨论文件: id_101000.xlsx -> 101000
        return filename.replace('id_', '').replace('.xlsx', '')
    else:
        # 人人讨论文件需要通过其他方式确定学生ID
        return None

def get_coding_categories(df):
    """获取所有编码类别"""
    categories = set()
    
    for col in ['Sub Category0', 'Sub Category1', 'Sub Category2']:
        if col in df.columns:
            non_null_values = df[col].dropna()
            categories.update(non_null_values.tolist())
    
    return sorted(list(categories))

def analyze_human_ai_discussions(human_ai_files, id_to_name):
    """分析人AI讨论"""
    print("分析人AI讨论...")
    
    results = defaultdict(lambda: defaultdict(int))
    all_categories = set()
    
    for file in human_ai_files:
        try:
            filename = os.path.basename(file)
            student_id = extract_student_id_from_filename(filename)
            
            if not student_id or student_id not in id_to_name:
                continue
                
            student_name = id_to_name[student_id]
            
            # 读取文件
            df = pd.read_excel(file)
            
            # 统计编码
            student_total = 0
            for col in ['Sub Category0', 'Sub Category1', 'Sub Category2']:
                if col in df.columns:
                    non_null_values = df[col].dropna()
                    for category in non_null_values:
                        if pd.notna(category) and category.strip():
                            results[student_name][f"人AI_{category}"] += 1
                            student_total += 1
                            all_categories.add(category)
            
            results[student_name]['人AI_总数'] = student_total
            
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")
    
    print(f"人AI讨论分析完成，涉及 {len(results)} 个学生，{len(all_categories)} 个类别")
    return results, all_categories

def create_speaker_mapping():
    """创建说话人到学生的映射"""
    print("创建说话人到学生的映射...")

    # 加载学生数据
    df = pd.read_csv('group_collaboration_data_all_new.csv')

    # 创建映射：(date_group, group, speaker_num) -> student_name
    speaker_mapping = {}

    for _, row in df.iterrows():
        if pd.notna(row['Date_Group']) and pd.notna(row['Group']) and pd.notna(row['说话人编号']) and pd.notna(row['人名']):
            date_group = int(row['Date_Group'])
            group = int(row['Group'])
            speaker_num = int(row['说话人编号'])
            student_name = row['人名']

            key = (date_group, group, speaker_num)
            speaker_mapping[key] = student_name

    print(f"创建了 {len(speaker_mapping)} 个说话人映射")
    # print(speaker_mapping)
    # exit(0)
    return speaker_mapping

def extract_date_group_from_filename(filename):
    """从文件名中提取date_group"""
    # 文件名格式: 0110——1.xlsx -> date_group=1001, group=1
    match = re.match(r'^(\d{4})——(\d+)', filename)
    if match:
        date_prefix = match.group(1)  # 0110
        group_num = int(match.group(2))  # 1

        # 将0110转换为1001格式
        if date_prefix == '0110':
            date_group = 1001
        elif date_prefix == '0113':
            date_group = 1301  # 根据实际数据调整
        elif date_prefix == '0114':
            date_group = 1401
        elif date_prefix == '0115':
            date_group = 1501
        elif date_prefix == '0116':
            date_group = 1601
        else:
            date_group = None

        return date_group, group_num

    return None, None

def analyze_human_human_discussions(human_human_files, id_to_name):
    """分析人人讨论"""
    print("分析人人讨论...")

    results = defaultdict(lambda: defaultdict(int))
    all_categories = set()

    # 创建说话人映射
    speaker_mapping = create_speaker_mapping()

    for file in human_human_files:
        try:
            filename = os.path.basename(file)
            date_group, group_num = extract_date_group_from_filename(filename)

            if date_group is None or group_num is None:
                print(f"无法解析文件名: {filename}")
                continue

            df = pd.read_excel(file)

            # 检查是否有说话人列
            if '说话人' not in df.columns:
                continue

            # 获取文件中的编码类别
            file_categories = get_coding_categories(df)
            all_categories.update(file_categories)

            # 分析每一行
            for _, row in df.iterrows():
                speaker = row.get('说话人', '')

                # 处理可能的NaN值
                if pd.isna(speaker):
                    continue

                speaker = str(speaker)

                # 提取说话人编号
                speaker_match = re.match(r'说话人(\d+)', speaker)
                if not speaker_match:
                    continue

                speaker_num = int(speaker_match.group(1))

                # 查找对应的学生
                key = (date_group, group_num, speaker_num)
                if key not in speaker_mapping:
                    continue

                student_name = speaker_mapping[key]

                # 统计编码
                for col in ['Sub Category0', 'Sub Category1', 'Sub Category2']:
                    if col in df.columns and pd.notna(row[col]):
                        category = str(row[col]).strip()
                        if category and category != 'nan':
                            results[student_name][f"人人_{category}"] += 1
                            all_categories.add(category)

                # 统计该学生的总编码数
                student_coding_count = 0
                for col in ['Sub Category0', 'Sub Category1', 'Sub Category2']:
                    if col in df.columns and pd.notna(row[col]):
                        category = str(row[col]).strip()
                        if category and category != 'nan':
                            student_coding_count += 1

                if student_coding_count > 0:
                    results[student_name]['人人_总数'] += student_coding_count

        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")

    print(f"人人讨论分析完成，涉及 {len(results)} 个学生，发现 {len(all_categories)} 个类别")
    return results, all_categories

def create_output_dataframe(human_ai_results, human_human_results, all_categories):
    """创建输出数据框"""
    print("创建输出数据框...")
    
    # 合并所有学生
    all_students = set(human_ai_results.keys()) | set(human_human_results.keys())
    all_categories = sorted(list(all_categories))
    
    # 创建列名
    columns = ['学生姓名', '学生ID']
    
    # 人AI讨论列
    columns.extend([f'人AI_{cat}_数量' for cat in all_categories])
    columns.extend([f'人AI_{cat}_占比' for cat in all_categories])
    columns.append('人AI_总编码数')
    
    # 人人讨论列
    columns.extend([f'人人_{cat}_数量' for cat in all_categories])
    columns.extend([f'人人_{cat}_占比' for cat in all_categories])
    columns.append('人人_总编码数')
    
    # 创建数据
    data = []
    id_to_name = load_student_mapping()
    name_to_id = {v: k for k, v in id_to_name.items()}
    
    for student_name in sorted(all_students):
        row = [student_name, name_to_id.get(student_name, '')]
        
        # 人AI讨论数据
        ai_total = human_ai_results[student_name].get('人AI_总数', 0)
        
        for cat in all_categories:
            count = human_ai_results[student_name].get(f'人AI_{cat}', 0)
            row.append(count)
        
        for cat in all_categories:
            count = human_ai_results[student_name].get(f'人AI_{cat}', 0)
            percentage = (count / ai_total * 100) if ai_total > 0 else 0
            row.append(round(percentage, 2))
        
        row.append(ai_total)
        
        # 人人讨论数据
        human_total = human_human_results[student_name].get('人人_总数', 0)
        
        for cat in all_categories:
            count = human_human_results[student_name].get(f'人人_{cat}', 0)
            row.append(count)
        
        for cat in all_categories:
            count = human_human_results[student_name].get(f'人人_{cat}', 0)
            percentage = (count / human_total * 100) if human_total > 0 else 0
            row.append(round(percentage, 2))
        
        row.append(human_total)
        
        data.append(row)
    
    df = pd.DataFrame(data, columns=columns)
    return df

def main():
    """主函数"""
    print("开始分析学生编码...")
    print("=" * 50)
    
    try:
        # 1. 加载学生映射
        id_to_name = load_student_mapping()
        
        # 2. 获取文件列表
        human_human_files, human_ai_files = get_file_lists()
        
        # 3. 分析人AI讨论
        human_ai_results, ai_categories = analyze_human_ai_discussions(human_ai_files, id_to_name)
        
        # 4. 分析人人讨论（暂时跳过，需要更多信息）
        human_human_results, human_categories = analyze_human_human_discussions(human_human_files, id_to_name)
        
        # 5. 合并所有类别
        all_categories = ai_categories | human_categories
        
        # 6. 创建输出数据框
        result_df = create_output_dataframe(human_ai_results, human_human_results, all_categories)
        
        # 7. 保存结果
        output_file = 'student_coding_analysis.xlsx'
        result_df.to_excel(output_file, index=False)
        
        print(f"\n分析完成！结果已保存到 {output_file}")
        print(f"共分析了 {len(result_df)} 个学生")
        print(f"发现了 {len(all_categories)} 个编码类别: {sorted(list(all_categories))}")
        
        # 显示统计信息
        print(f"\n人AI讨论统计:")
        ai_students = len([s for s in result_df['学生姓名'] if result_df[result_df['学生姓名']==s]['人AI_总编码数'].iloc[0] > 0])
        print(f"  参与学生数: {ai_students}")
        print(f"  总编码数: {result_df['人AI_总编码数'].sum()}")
        
        print(f"\n人人讨论统计:")
        human_students = len([s for s in result_df['学生姓名'] if result_df[result_df['学生姓名']==s]['人人_总编码数'].iloc[0] > 0])
        print(f"  参与学生数: {human_students}")
        print(f"  总编码数: {result_df['人人_总编码数'].sum()}")
        
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
