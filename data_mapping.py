#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据映射脚本：构建 id - 人名 - 说话人x 的对应关系

从 group_collaboration_data_all.csv 中找到的 id，
先去 MAIC对照实验信息.xlsx 表格中找到对应的人名，
然后再去 说话人对应任务.xlsx 中找到人名对应的列是说话人几号，
最终把结果存到 group_collaboration_data_all_new.csv 中
"""

import pandas as pd
import numpy as np
import os

def load_data():
    """加载所有需要的数据文件"""
    print("正在加载数据文件...")
    
    # 加载主数据文件
    main_data = pd.read_csv('group_collaboration_data_all.csv')
    print(f"主数据文件加载完成，共 {len(main_data)} 行")
    
    # 加载MAIC对照实验信息
    maic_info = pd.read_excel('MAIC对照实验信息.xlsx')
    print(f"MAIC对照实验信息加载完成，共 {len(maic_info)} 行")
    
    # 加载说话人对应任务信息
    speaker_info = pd.read_excel('说话人对应任务.xlsx')
    print(f"说话人对应任务信息加载完成，共 {len(speaker_info)} 行")
    
    return main_data, maic_info, speaker_info

def create_id_name_mapping(maic_info):
    """创建 id 到姓名的映射"""
    print("创建 id 到姓名的映射...")
    
    # 创建编号到姓名的映射字典
    id_to_name = {}
    for _, row in maic_info.iterrows():
        id_val = row['编号']
        name = row['姓名']
        if pd.notna(id_val) and pd.notna(name):
            id_to_name[id_val] = name
    
    print(f"创建了 {len(id_to_name)} 个 id-姓名 映射")
    return id_to_name

def create_name_speaker_mapping(speaker_info):
    """创建姓名到说话人编号的映射"""
    print("创建姓名到说话人编号的映射...")
    
    name_to_speaker = {}
    
    # 遍历每一行，为每个说话人创建映射
    for _, row in speaker_info.iterrows():
        video_name = row['视频名称']
        
        # 处理说话人1、2、3
        for i in range(1, 4):
            speaker_col = f'说话人{i}'
            if speaker_col in row and pd.notna(row[speaker_col]):
                name = row[speaker_col]
                # 如果这个人名还没有映射，或者当前视频名更小（优先使用较早的映射）
                if name not in name_to_speaker:
                    name_to_speaker[name] = i
                # 如果已存在映射，检查是否一致
                elif name_to_speaker[name] != i:
                    print(f"警告：{name} 在不同视频中对应不同的说话人编号")
    
    print(f"创建了 {len(name_to_speaker)} 个 姓名-说话人编号 映射")
    return name_to_speaker

def add_mapping_columns(main_data, id_to_name, name_to_speaker):
    """为主数据添加人名和说话人编号列"""
    print("为主数据添加映射列...")
    
    # 创建新的数据框副本
    new_data = main_data.copy()
    
    # 添加人名列
    new_data['人名'] = new_data['id'].map(id_to_name)
    
    # 添加说话人编号列
    new_data['说话人编号'] = new_data['人名'].map(name_to_speaker)
    
    # 统计映射成功的数量
    name_mapped = new_data['人名'].notna().sum()
    speaker_mapped = new_data['说话人编号'].notna().sum()
    
    print(f"成功映射人名：{name_mapped}/{len(new_data)} 行")
    print(f"成功映射说话人编号：{speaker_mapped}/{len(new_data)} 行")
    
    return new_data

def analyze_mapping_results(new_data):
    """分析映射结果"""
    print("\n=== 映射结果分析 ===")
    
    # 统计未映射到人名的id
    unmapped_names = new_data[new_data['人名'].isna()]['id'].unique()
    if len(unmapped_names) > 0:
        print(f"未找到人名的id ({len(unmapped_names)}个):")
        for id_val in sorted(unmapped_names):
            print(f"  {id_val}")
    
    # 统计未映射到说话人编号的人名
    unmapped_speakers = new_data[new_data['人名'].notna() & new_data['说话人编号'].isna()]['人名'].unique()
    if len(unmapped_speakers) > 0:
        print(f"未找到说话人编号的人名 ({len(unmapped_speakers)}个):")
        for name in sorted(unmapped_speakers):
            print(f"  {name}")
    
    # 统计说话人编号分布
    speaker_counts = new_data['说话人编号'].value_counts().sort_index()
    print(f"\n说话人编号分布:")
    for speaker_num, count in speaker_counts.items():
        print(f"  说话人{int(speaker_num)}: {count} 条记录")
    
    # 显示一些成功映射的示例
    successful_mappings = new_data[new_data['人名'].notna() & new_data['说话人编号'].notna()]
    if len(successful_mappings) > 0:
        print(f"\n成功映射示例 (前10条):")
        sample_data = successful_mappings[['id', '人名', '说话人编号']].head(10)
        for _, row in sample_data.iterrows():
            print(f"  id: {row['id']} -> 人名: {row['人名']} -> 说话人{int(row['说话人编号'])}")

def save_results(new_data, output_filename='group_collaboration_data_all_new.csv'):
    """保存结果到新文件"""
    print(f"\n保存结果到 {output_filename}...")
    
    # 保存到新的CSV文件
    new_data.to_csv(output_filename, index=False, encoding='utf-8-sig')
    
    print(f"结果已保存到 {output_filename}")
    print(f"新文件包含 {len(new_data)} 行，{len(new_data.columns)} 列")
    
    # 显示新增的列
    original_columns = pd.read_csv('group_collaboration_data_all.csv').columns.tolist()
    new_columns = [col for col in new_data.columns if col not in original_columns]
    print(f"新增列: {new_columns}")

def main():
    """主函数"""
    print("开始执行数据映射任务...")
    print("=" * 50)
    
    try:
        # 1. 加载数据
        main_data, maic_info, speaker_info = load_data()
        
        # 2. 创建映射关系
        id_to_name = create_id_name_mapping(maic_info)
        name_to_speaker = create_name_speaker_mapping(speaker_info)
        
        # 3. 添加映射列
        new_data = add_mapping_columns(main_data, id_to_name, name_to_speaker)
        
        # 4. 分析结果
        analyze_mapping_results(new_data)
        
        # 5. 保存结果
        save_results(new_data)
        
        print("\n" + "=" * 50)
        print("数据映射任务完成！")
        
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
